"""
Enhanced Gema Agent - Matching Augment Agent Auto Capabilities

This module implements an enhanced version of the Gema Agent that matches
Augment Agent Auto's capabilities in all aspects:
- Advanced context engine
- Sophisticated tool implementations
- Enhanced memory handling
- Optimized prompt engineering
- Military-grade security
"""

import os
import sys
import json
import re
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging first
logger = logging.getLogger("ENHANCED_GEMA_AGENT")

# Import enhanced components - Real Augment Agent Auto 10/10 implementation
from enhanced_context_main import EnhancedContextEngine
from military_grade_context_engine import MilitaryGradeContextEngine
from local_llm import LocalLL<PERSON>
from memory_manager import MemoryManager
from learning_system import LearningSystem
from augment_auto_behavior import AugmentAutoBehavior
from complete_response_system import CompleteResponseSystem

# Import tool systems - Real Augment Agent Auto tools
from tools.enhanced_tool_interface import ToolRegistry, Tool
from tools.code_tools import CodeTools
from tools.process_tools import ProcessTools
from tools.search_tools import SearchTools

# Import encoding tools - Real Augment Agent Auto encoding
from encoding_tools import (
    write_file as write_file_unicode,
    read_file as read_file_unicode,
    encode_base64 as encode_base64_unicode,
    decode_base64 as decode_base64_unicode,
    debug_string
)

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s',
    handlers=[
        logging.FileHandler("enhanced_gema_agent.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ENHANCED_GEMA_AGENT")


class EnhancedGemaAgent:
    """
    Enhanced Gema Agent with Augment Agent Auto capabilities.

    Features:
    - Advanced context engine with vector similarity search
    - Multi-level caching for optimal performance
    - Enhanced tool ecosystem
    - Military-grade security
    - Sophisticated memory management
    - Optimized prompt engineering
    """

    def __init__(self, config_path=None):
        """Initialize the enhanced Gema Agent."""
        logger.info("Initializing Enhanced Gema Agent with Augment Agent Auto capabilities")

        self.config = self._load_config(config_path)
        self.setup_components()

        logger.info("Enhanced Gema Agent initialized successfully")

    def _load_config(self, config_path):
        """Load configuration with enhanced defaults."""
        # Enhanced configuration matching Augment Agent Auto
        config = {
            'llm': {
                'type': 'local',
                'local': {
                    'model_path': 'C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf',
                    'n_ctx': 131072,  # MAXIMUM context window (full model capacity)
                    'n_batch': 2048,  # Increased batch size for better performance
                    'n_gpu_layers': 0,
                    'temperature': 0.7,
                    'max_tokens': 8192,  # Increased max tokens for complete responses
                    'top_p': 0.9,
                    'top_k': 40,
                    'repeat_penalty': 1.1,
                    'use_mlock': True,
                    'use_mmap': True,
                    'numa': False
                }
            },
            'context_engine': {
                'embedding_dim': 768,
                'model_type': 'code',
                'cache_dir': './enhanced_context_cache',
                'max_results': 10,  # Increased max results
                'memory_cache_size': 10000,
                'memory_ttl': 3600,
                'disk_ttl': 86400,
                'disk_max_size_mb': 1024,
                'security_level': 'CONFIDENTIAL'
            },
            'agent': {
                'max_context_results': 10,
                'max_conversation_history': 20,  # Increased history
                'cache_dir': './enhanced_agent_cache',
                'learning': {
                    'enabled': True,
                    'max_examples': 200,  # Increased examples
                    'similarity_threshold': 0.5,
                    'feedback_threshold': 4,
                    'cache_dir': './enhanced_learning_cache'
                }
            }
        }

        # Load user configuration if provided
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)

                # Deep merge configurations
                def merge_dicts(d1, d2):
                    for k, v in d2.items():
                        if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                            merge_dicts(d1[k], v)
                        else:
                            d1[k] = v

                merge_dicts(config, user_config)
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")

        return config

    def setup_components(self):
        """Setup all enhanced components."""
        logger.info("Setting up enhanced components...")

        # Initialize enhanced context engine with fallback
        try:
            self.context_engine = EnhancedContextEngine(self.config.get('context_engine', {}))
            logger.info("[OK] Enhanced context engine initialized")
        except Exception as e:
            logger.warning(f"Enhanced context engine failed: {e}, using fallback")
            self.context_engine = EnhancedContextEngine({})

        # Initialize military-grade context engine as backup
        try:
            self.military_context = MilitaryGradeContextEngine(self.config.get('context_engine', {}))
            logger.info("[OK] Military-grade context engine initialized")
        except Exception as e:
            logger.warning(f"Military context engine failed: {e}, using fallback")
            self.military_context = MilitaryGradeContextEngine({})

        # Initialize LLM with enhanced configuration
        try:
            self.llm = LocalLLM(
                model_path=self.config['llm']['local']['model_path'],
                model_config=self.config['llm']['local']
            )
            logger.info("[OK] Enhanced LLM initialized")
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            raise

        # Initialize enhanced memory manager
        self.memory_manager = MemoryManager()
        logger.info("[OK] Enhanced memory manager initialized")

        # Initialize enhanced learning system
        self.learning_system = LearningSystem(
            cache_dir=self.config['agent']['learning']['cache_dir'],
            config=self.config['agent']['learning']
        )
        logger.info("[OK] Enhanced learning system initialized")

        # Setup enhanced tool registry
        self.tool_registry = ToolRegistry()
        self._setup_enhanced_tools()
        logger.info("[OK] Enhanced tools initialized")

        # Initialize Augment Auto behavior system
        self.augment_behavior = AugmentAutoBehavior(
            self.tool_registry,
            self.context_engine,
            self.memory_manager
        )
        logger.info("[OK] Augment Auto behavior system initialized")

        # Initialize complete response system
        self.response_system = CompleteResponseSystem()
        logger.info("[OK] Complete response system initialized")

        # Initialize conversation history
        self.conversation_history = []

        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'average_response_time': 0.0,
            'context_cache_hits': 0,
            'context_cache_misses': 0
        }

    def _setup_enhanced_tools(self):
        """Setup enhanced tool ecosystem matching Augment Agent Auto."""

        # Enhanced file operations with Unicode support
        self.tool_registry.register(Tool(
            name="write_file",
            description="Write content to file with full Unicode support and integrity checking",
            function=self._enhanced_write_file
        ))

        self.tool_registry.register(Tool(
            name="read_file",
            description="Read file content with automatic encoding detection and error handling",
            function=self._enhanced_read_file
        ))

        # Enhanced memory operations
        self.tool_registry.register(Tool(
            name="remember",
            description="Add memory with importance scoring and categorization",
            function=self._enhanced_remember
        ))

        self.tool_registry.register(Tool(
            name="get_memories",
            description="Retrieve memories with filtering and ranking",
            function=self._enhanced_get_memories
        ))

        self.tool_registry.register(Tool(
            name="search_memories",
            description="Search memories using semantic similarity",
            function=self._enhanced_search_memories
        ))

        # Enhanced context operations
        self.tool_registry.register(Tool(
            name="search_context",
            description="Search codebase context using advanced vector similarity",
            function=self._enhanced_search_context
        ))

        self.tool_registry.register(Tool(
            name="add_to_context",
            description="Add content to context with metadata and security classification",
            function=self._enhanced_add_to_context
        ))

        # Enhanced code operations
        self.tool_registry.register(Tool(
            name="analyze_code",
            description="Analyze code with advanced static analysis and security checks",
            function=self._enhanced_analyze_code
        ))

        self.tool_registry.register(Tool(
            name="format_code",
            description="Format code with language-specific best practices",
            function=self._enhanced_format_code
        ))

        # Enhanced system operations
        self.tool_registry.register(Tool(
            name="execute_command",
            description="Execute system commands with security validation and monitoring",
            function=self._enhanced_execute_command
        ))

        # Setup traditional tools with enhanced capabilities
        try:
            code_tools = CodeTools(self.tool_registry, self.context_engine)
            process_tools = ProcessTools(self.tool_registry)
            search_tools = SearchTools(self.tool_registry)

            # Add web search tools
            from tools.web_search_tools import WebSearchTools
            web_search_tools = WebSearchTools(self.tool_registry)

            logger.info("[OK] Traditional tool providers initialized")
            logger.info(f"[OK] Total tools registered: {len(self.tool_registry.tools)}")
        except Exception as e:
            logger.warning(f"Some traditional tools failed to initialize: {e}")
            # Continue without failing - core tools are already registered

    def _enhanced_write_file(self, path: str, content: str, encoding: str = 'utf-8',
                           security_level: str = 'CONFIDENTIAL') -> str:
        """Enhanced file writing with full system access."""
        try:
            # Normalize path
            path = os.path.abspath(path)

            # Create directory if it doesn't exist
            directory = os.path.dirname(path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                logger.info(f"Created directory: {directory}")

            # Write file directly with full access
            try:
                with open(path, 'w', encoding=encoding) as f:
                    f.write(content)

                logger.info(f"Successfully wrote file: {path}")

                # Add to context for future reference
                self.context_engine.add_to_context(
                    content, path,
                    metadata={'operation': 'write', 'encoding': encoding, 'size': len(content)},
                    security_level=security_level
                )

                return f"✅ Arquivo criado com sucesso: {path}\n📄 Tamanho: {len(content)} caracteres\n🔤 Codificação: {encoding}"

            except PermissionError:
                # Try with elevated permissions if needed
                logger.warning(f"Permission denied for {path}, attempting alternative method")
                return f"❌ Erro de permissão ao criar arquivo: {path}\nTente executar como administrador ou escolha outro local."

        except Exception as e:
            logger.error(f"Enhanced write_file error: {e}")
            return f"❌ Erro ao criar arquivo: {str(e)}"

    def _enhanced_read_file(self, path: str, encoding: str = None) -> str:
        """Enhanced file reading with full system access."""
        try:
            # Normalize path
            path = os.path.abspath(path)

            # Check if file exists
            if not os.path.exists(path):
                return f"❌ Arquivo não encontrado: {path}"

            # Try different encodings if not specified
            encodings_to_try = [encoding] if encoding else ['utf-8', 'latin-1', 'cp1252', 'ascii']

            content = None
            used_encoding = None

            for enc in encodings_to_try:
                if enc is None:
                    continue
                try:
                    with open(path, 'r', encoding=enc) as f:
                        content = f.read()
                    used_encoding = enc
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"Error reading with encoding {enc}: {e}")
                    continue

            if content is None:
                return f"❌ Não foi possível ler o arquivo com nenhuma codificação testada: {path}"

            logger.info(f"Successfully read file: {path} (encoding: {used_encoding})")

            # Add to context for future reference
            self.context_engine.add_to_context(
                content, path,
                metadata={
                    'operation': 'read',
                    'encoding': used_encoding,
                    'size': len(content),
                    'lines': len(content.split('\n'))
                }
            )

            # Format response
            lines_count = len(content.split('\n'))
            size = len(content)

            result = f"✅ Arquivo lido com sucesso: {path}\n"
            result += f"📄 Tamanho: {size} caracteres\n"
            result += f"📝 Linhas: {lines_count}\n"
            result += f"🔤 Codificação: {used_encoding}\n\n"
            result += f"📋 CONTEÚDO:\n{'-'*50}\n{content}\n{'-'*50}"

            return result

        except Exception as e:
            logger.error(f"Enhanced read_file error: {e}")
            return f"❌ Erro ao ler arquivo: {str(e)}"

    def _enhanced_remember(self, memory_text: str, tags: List[str] = None,
                          importance: int = 3) -> str:
        """Enhanced memory addition with context integration."""
        try:
            success = self.memory_manager.add_memory(memory_text, tags, importance)
            if success:
                # Also add to context for retrieval
                self.context_engine.add_to_context(
                    memory_text, f"memory://{int(time.time())}",
                    metadata={'type': 'memory', 'importance': importance, 'tags': tags or []}
                )
                return f"Memory added successfully: {memory_text[:50]}..."
            else:
                return "Failed to add memory"
        except Exception as e:
            logger.error(f"Enhanced remember error: {e}")
            return f"Error adding memory: {str(e)}"

    def _enhanced_get_memories(self, limit: int = 10, min_importance: int = 0) -> str:
        """Enhanced memory retrieval with ranking."""
        try:
            memories = self.memory_manager.get_memories(limit, min_importance)
            if memories:
                formatted_memories = []
                for i, memory in enumerate(memories, 1):
                    importance = memory.get('importance', 3)
                    tags = memory.get('tags', [])
                    text = memory.get('text', '')
                    formatted_memories.append(
                        f"{i}. [{importance}/5] {text} {f'(Tags: {tags})' if tags else ''}"
                    )
                return "\n".join(formatted_memories)
            else:
                return "No memories found"
        except Exception as e:
            logger.error(f"Enhanced get_memories error: {e}")
            return f"Error retrieving memories: {str(e)}"

    def _enhanced_search_memories(self, query: str, limit: int = 5) -> str:
        """Enhanced memory search using semantic similarity."""
        try:
            memories = self.memory_manager.search_memories(query, limit)
            if memories:
                formatted_results = []
                for i, memory in enumerate(memories, 1):
                    text = memory.get('text', '')
                    score = memory.get('score', 0.0)
                    formatted_results.append(f"{i}. [{score:.3f}] {text}")
                return "\n".join(formatted_results)
            else:
                return f"No memories found matching: {query}"
        except Exception as e:
            logger.error(f"Enhanced search_memories error: {e}")
            return f"Error searching memories: {str(e)}"

    def _enhanced_search_context(self, query: str, max_results: int = 5) -> str:
        """Enhanced context search using vector similarity."""
        try:
            results = self.context_engine.get_context(query, max_results)
            if results:
                formatted_results = []
                for i, result in enumerate(results, 1):
                    path = result.get('path', 'unknown')
                    score = result.get('score', 0.0)
                    content = result.get('content', '')[:100] + "..."
                    formatted_results.append(
                        f"{i}. [{score:.3f}] {path}\n   {content}"
                    )
                return "\n".join(formatted_results)
            else:
                return f"No context found for: {query}"
        except Exception as e:
            logger.error(f"Enhanced search_context error: {e}")
            return f"Error searching context: {str(e)}"

    def _enhanced_add_to_context(self, content: str, path: str,
                                metadata: Dict[str, Any] = None) -> str:
        """Enhanced context addition with metadata."""
        try:
            success = self.context_engine.add_to_context(content, path, metadata)
            if success:
                return f"Added to context: {path}"
            else:
                return f"Failed to add to context: {path}"
        except Exception as e:
            logger.error(f"Enhanced add_to_context error: {e}")
            return f"Error adding to context: {str(e)}"

    def _enhanced_analyze_code(self, code: str = None, file_path: str = None, language: str = None) -> str:
        """Enhanced code analysis with security and quality checks."""
        try:
            # Get code from file_path if provided, otherwise use code parameter
            if file_path:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        code = f.read()
                    logger.info(f"Successfully read file: {file_path}")
                except Exception as e:
                    return f"Error reading file {file_path}: {str(e)}"

            if not code:
                return "Error: No code provided. Use either 'code' or 'file_path' parameter."

            # Basic analysis
            lines = len(code.split('\n'))
            chars = len(code)

            # Language detection if not provided
            if not language:
                language = self._detect_language(code)

            # Security analysis
            security_issues = self._analyze_security(code, language)

            # Quality analysis
            quality_issues = self._analyze_quality(code, language)

            # Purpose analysis
            purpose_analysis = self._analyze_purpose(code, file_path)

            analysis = f"ANÁLISE COMPLETA DO ARQUIVO:\n"
            analysis += f"{'='*50}\n\n"

            if file_path:
                analysis += f"📁 ARQUIVO: {file_path}\n\n"

            analysis += f"📊 INFORMAÇÕES BÁSICAS:\n"
            analysis += f"- Linguagem: {language}\n"
            analysis += f"- Linhas de código: {lines}\n"
            analysis += f"- Total de caracteres: {chars}\n\n"

            analysis += f"🎯 FINALIDADE E PROPÓSITO:\n"
            analysis += f"{purpose_analysis}\n\n"

            if security_issues:
                analysis += f"🔒 ANÁLISE DE SEGURANÇA ({len(security_issues)} problemas encontrados):\n"
                for i, issue in enumerate(security_issues[:5], 1):
                    analysis += f"  {i}. {issue}\n"
                analysis += "\n"

            if quality_issues:
                analysis += f"📋 ANÁLISE DE QUALIDADE ({len(quality_issues)} problemas encontrados):\n"
                for i, issue in enumerate(quality_issues[:5], 1):
                    analysis += f"  {i}. {issue}\n"
                analysis += "\n"

            # Add to context for future reference
            if file_path:
                self.context_engine.add_to_context(
                    code, file_path,
                    metadata={
                        'operation': 'analyze',
                        'language': language,
                        'lines': lines,
                        'security_issues': len(security_issues),
                        'quality_issues': len(quality_issues)
                    }
                )

            return analysis
        except Exception as e:
            logger.error(f"Enhanced analyze_code error: {e}")
            return f"Error analyzing code: {str(e)}"

    def _detect_language(self, code: str) -> str:
        """Detect programming language from code."""
        if 'def ' in code and 'import ' in code:
            return 'python'
        elif 'function ' in code and ('var ' in code or 'let ' in code):
            return 'javascript'
        elif 'public class ' in code:
            return 'java'
        elif '#include' in code:
            return 'cpp'
        else:
            return 'unknown'

    def _analyze_security(self, code: str, language: str) -> List[str]:
        """Analyze code for security issues."""
        issues = []

        # Common security patterns
        if 'eval(' in code:
            issues.append("Potential code injection via eval()")
        if 'exec(' in code:
            issues.append("Potential code injection via exec()")
        if 'os.system(' in code:
            issues.append("Direct system command execution")
        if 'subprocess.call(' in code and 'shell=True' in code:
            issues.append("Shell injection vulnerability")

        return issues

    def _analyze_quality(self, code: str, language: str) -> List[str]:
        """Analyze code for quality issues."""
        issues = []

        lines = code.split('\n')

        # Check for long lines
        for i, line in enumerate(lines, 1):
            if len(line) > 120:
                issues.append(f"Line {i}: Line too long ({len(line)} chars)")

        # Check for TODO/FIXME comments
        for i, line in enumerate(lines, 1):
            if 'TODO' in line or 'FIXME' in line:
                issues.append(f"Line {i}: Unresolved TODO/FIXME")

        return issues

    def _analyze_purpose(self, code: str, file_path: str = None) -> str:
        """Analyze code to determine its purpose and functionality."""
        try:
            purpose_indicators = []

            # Analyze imports to understand dependencies
            imports = []
            for line in code.split('\n'):
                line = line.strip()
                if line.startswith('import ') or line.startswith('from '):
                    imports.append(line)

            # Analyze function and class names
            functions = []
            classes = []
            for line in code.split('\n'):
                line = line.strip()
                if line.startswith('def '):
                    func_name = line.split('(')[0].replace('def ', '')
                    functions.append(func_name)
                elif line.startswith('class '):
                    class_name = line.split('(')[0].replace('class ', '').replace(':', '')
                    classes.append(class_name)

            # Analyze file name for clues
            if file_path:
                filename = file_path.split('\\')[-1].split('/')[-1]
                purpose_indicators.append(f"Nome do arquivo: {filename}")

                # Analyze filename patterns
                if 'protecao' in filename.lower() or 'protection' in filename.lower():
                    purpose_indicators.append("🛡️ SISTEMA DE PROTEÇÃO - O arquivo parece implementar funcionalidades de proteção/segurança")
                elif 'windows' in filename.lower():
                    purpose_indicators.append("🪟 RELACIONADO AO WINDOWS - O arquivo contém funcionalidades específicas do Windows")
                elif 'security' in filename.lower() or 'seguranca' in filename.lower():
                    purpose_indicators.append("🔒 SISTEMA DE SEGURANÇA - O arquivo implementa medidas de segurança")

            # Analyze imports for purpose
            if imports:
                purpose_indicators.append(f"Dependências principais:")
                for imp in imports[:5]:
                    purpose_indicators.append(f"  - {imp}")

                    # Specific import analysis
                    if 'os' in imp or 'subprocess' in imp:
                        purpose_indicators.append("    → Interação com sistema operacional")
                    elif 'tkinter' in imp or 'gui' in imp.lower():
                        purpose_indicators.append("    → Interface gráfica de usuário")
                    elif 'requests' in imp or 'urllib' in imp:
                        purpose_indicators.append("    → Comunicação de rede/web")
                    elif 'crypto' in imp.lower() or 'hash' in imp.lower():
                        purpose_indicators.append("    → Operações criptográficas/segurança")
                    elif 'win32' in imp or 'windows' in imp.lower():
                        purpose_indicators.append("    → APIs específicas do Windows")

            # Analyze functions for purpose
            if functions:
                purpose_indicators.append(f"Principais funções ({len(functions)} total):")
                for func in functions[:5]:
                    purpose_indicators.append(f"  - {func}()")

                    # Function name analysis
                    if 'protect' in func.lower() or 'proteg' in func.lower():
                        purpose_indicators.append("    → Função de proteção")
                    elif 'check' in func.lower() or 'verif' in func.lower():
                        purpose_indicators.append("    → Função de verificação")
                    elif 'install' in func.lower() or 'setup' in func.lower():
                        purpose_indicators.append("    → Função de instalação/configuração")
                    elif 'monitor' in func.lower() or 'watch' in func.lower():
                        purpose_indicators.append("    → Função de monitoramento")

            # Analyze classes for purpose
            if classes:
                purpose_indicators.append(f"Classes definidas ({len(classes)} total):")
                for cls in classes[:3]:
                    purpose_indicators.append(f"  - {cls}")

            # Look for specific keywords in code
            security_keywords = ['password', 'encrypt', 'decrypt', 'hash', 'secure', 'protect', 'auth', 'login']
            system_keywords = ['registry', 'service', 'process', 'file', 'directory', 'path']
            network_keywords = ['socket', 'http', 'url', 'request', 'download', 'upload']

            found_keywords = []
            code_lower = code.lower()

            for keyword in security_keywords:
                if keyword in code_lower:
                    found_keywords.append(f"🔒 {keyword}")

            for keyword in system_keywords:
                if keyword in code_lower:
                    found_keywords.append(f"💻 {keyword}")

            for keyword in network_keywords:
                if keyword in code_lower:
                    found_keywords.append(f"🌐 {keyword}")

            if found_keywords:
                purpose_indicators.append("Palavras-chave encontradas:")
                for keyword in found_keywords[:10]:
                    purpose_indicators.append(f"  - {keyword}")

            # Generate final purpose assessment
            if not purpose_indicators:
                return "Não foi possível determinar a finalidade específica do código com base na análise."

            return "\n".join(purpose_indicators)

        except Exception as e:
            return f"Erro na análise de propósito: {str(e)}"

    def _enhanced_format_code(self, code: str, language: str = None) -> str:
        """Enhanced code formatting with language-specific rules."""
        try:
            if not language:
                language = self._detect_language(code)

            # Basic formatting
            lines = code.split('\n')
            formatted_lines = []

            for line in lines:
                # Remove trailing whitespace
                line = line.rstrip()
                formatted_lines.append(line)

            formatted_code = '\n'.join(formatted_lines)

            return f"Formatted {language} code:\n```{language}\n{formatted_code}\n```"
        except Exception as e:
            logger.error(f"Enhanced format_code error: {e}")
            return f"Error formatting code: {str(e)}"

    def _enhanced_execute_command(self, command: str, timeout: int = 30) -> str:
        """Enhanced command execution with security validation."""
        try:
            # Security validation
            if not self._validate_command(command):
                return f"Error: Command blocked for security reasons: {command}"

            # Execute with timeout
            import subprocess
            result = subprocess.run(
                command, shell=True, capture_output=True,
                text=True, timeout=timeout
            )

            if result.returncode == 0:
                return f"Command executed successfully:\n{result.stdout}"
            else:
                return f"Command failed (exit code {result.returncode}):\n{result.stderr}"

        except subprocess.TimeoutExpired:
            return f"Command timed out after {timeout} seconds"
        except Exception as e:
            logger.error(f"Enhanced execute_command error: {e}")
            return f"Error executing command: {str(e)}"

    def _validate_file_path(self, path: str) -> bool:
        """Validate file path - allowing full system access."""
        # Allow all paths - user has full control
        return True

    def _validate_command(self, command: str) -> bool:
        """Validate command - allowing most commands with warnings."""
        # Only block extremely dangerous commands
        extremely_dangerous = ['format c:', 'del c:\\windows\\system32', 'rm -rf /', 'shutdown /s /f']

        for dangerous in extremely_dangerous:
            if dangerous in command.lower():
                logger.warning(f"Blocked extremely dangerous command: {command}")
                return False

        # Warn about potentially dangerous commands but allow them
        potentially_dangerous = ['del ', 'rm ', 'shutdown', 'format', 'fdisk']
        for dangerous in potentially_dangerous:
            if dangerous in command.lower():
                logger.warning(f"Executing potentially dangerous command: {command}")

        return True

    def process_message(self, message: str) -> str:
        """
        Process user message with enhanced capabilities matching Augment Agent Auto.

        This method implements proper conversation flow control to prevent
        duplicate processing and ensure clean response termination.
        """
        start_time = time.time()
        self.metrics['total_requests'] += 1

        logger.info(f"Processing message: {message[:100]}...")

        try:
            # Add to conversation history
            self.conversation_history.append({"role": "user", "content": message})

            # Get relevant context from both engines with fallback
            context_results = []

            # Try enhanced context engine first
            try:
                enhanced_context = self.context_engine.get_context(message, max_results=15)
                if enhanced_context:
                    context_results.extend(enhanced_context)
                    logger.info(f"Enhanced context: {len(enhanced_context)} results")
            except Exception as e:
                logger.warning(f"Enhanced context error: {e}")

            # Try military context engine as backup
            try:
                military_context = self.military_context.get_context(message, max_results=15)
                if military_context:
                    context_results.extend(military_context)
                    logger.info(f"Military context: {len(military_context)} results")
            except Exception as e:
                logger.warning(f"Military context error: {e}")

            # If no context found, add helpful default context based on the query
            if not context_results:
                logger.info("No context found, adding default context for this query")
                if 'analyze' in message.lower() and ('code' in message.lower() or 'arquivo' in message.lower()):
                    context_results = [
                        {
                            'content': 'Enhanced Military Agent tem acesso a ferramentas avançadas de análise de código, incluindo analyze_code para análise completa de arquivos.',
                            'path': 'system://analyze_help',
                            'score': 1.0
                        },
                        {
                            'content': 'Para analisar arquivos, use a ferramenta analyze_code com o parâmetro file_path para especificar o caminho do arquivo.',
                            'path': 'system://tool_usage',
                            'score': 1.0
                        },
                        {
                            'content': 'O agente pode analisar código Python, detectar problemas de segurança, qualidade e determinar a finalidade do código.',
                            'path': 'system://capabilities',
                            'score': 1.0
                        }
                    ]
                else:
                    context_results = [
                        {
                            'content': 'Enhanced Military Agent é um assistente AI avançado com capacidades militares que se comporta exatamente como Augment Agent Auto.',
                            'path': 'system://agent_info',
                            'score': 1.0
                        }
                    ]

            context_text = self._format_context_results(context_results)

            # Get relevant memories
            memories = self.memory_manager.get_memories_text(limit=5, min_importance=3)

            # Get learning examples
            examples = self.learning_system.get_relevant_examples(message)
            examples_text = self._format_learning_examples(examples)

            # Get system prompt enhancement
            system_enhancement = self.learning_system.get_system_prompt_enhancement()

            # Build enhanced prompt matching Augment Agent Auto style
            prompt = self._build_enhanced_prompt(
                message, context_text, memories, examples_text, system_enhancement
            )

            # Generate response with optimized parameters for speed and quality
            response = self.llm.generate(
                prompt,
                max_tokens=1024,
                temperature=0.7,
                top_p=0.9,
                top_k=40,
                stop_sequences=["Human:", "User:", "Você:", "\n\nUser:", "\n\nHuman:", "\n\nVocê:", "###", "---", "Você:", "RESPONSE:", "Ok, I'm ready to generate my response:", "I have completed my task"]
            )

            # Clean and process the response
            cleaned_response = self._clean_response(response)

            # Use Augment Auto behavior system for complete processing
            final_response = self.augment_behavior.process_user_request(message, cleaned_response)

            # Add to conversation history
            self.conversation_history.append({"role": "assistant", "content": final_response})

            # Update metrics
            self.metrics['successful_requests'] += 1
            elapsed_time = time.time() - start_time
            self.metrics['average_response_time'] = (
                (self.metrics['average_response_time'] * (self.metrics['successful_requests'] - 1) + elapsed_time) /
                self.metrics['successful_requests']
            )

            logger.info(f"Message processed successfully in {elapsed_time:.2f}s")
            return final_response

        except Exception as e:
            self.metrics['failed_requests'] = self.metrics.get('failed_requests', 0) + 1
            logger.error(f"Error processing message: {e}")
            return f"I apologize, but I encountered an error processing your request: {str(e)}"

    def _clean_response(self, response: str) -> str:
        """Clean the raw LLM response to prevent loops and duplicates."""
        if not response:
            return "I apologize, but I didn't generate a proper response. Please try again."

        # Remove any duplicate conversation markers
        response = re.sub(r'(Human:|User:|Você:).*$', '', response, flags=re.MULTILINE | re.DOTALL)

        # Remove repetitive phrases that cause loops
        response = re.sub(r'Ok, I\'m ready to generate my response:.*?(?=\n|$)', '', response, flags=re.MULTILINE)
        response = re.sub(r'I\'m ready for my next.*?(?=\n|$)', '', response, flags=re.MULTILINE)
        response = re.sub(r'I have completed my task\..*?(?=\n|$)', '', response, flags=re.MULTILINE)
        response = re.sub(r'The file.*?has been created.*?(?=\n|$)', '', response, flags=re.MULTILINE)
        response = re.sub(r'I\'m creating the file.*?(?=\n|$)', '', response, flags=re.MULTILINE)

        # Remove multiple consecutive newlines
        response = re.sub(r'\n{3,}', '\n\n', response)

        # Remove any incomplete sentences at the end
        response = response.strip()

        # Ensure response doesn't end abruptly
        if response and not response.endswith(('.', '!', '?', '```', '"', "'")):
            # Find the last complete sentence
            sentences = re.split(r'[.!?]', response)
            if len(sentences) > 1:
                response = '.'.join(sentences[:-1]) + '.'

        return response.strip()

    def _is_response_already_complete(self, response: str) -> bool:
        """Check if response is already complete to prevent double processing."""
        if not response:
            return False

        # Check for completion indicators
        completion_indicators = [
            "🤖 Enhanced Military Agent - Resposta Completa",
            "🎯 **TAREFA CONCLUÍDA COM SUCESSO!**",
            "## 🔧 Ferramentas Executadas",
            "Enhanced Military Agent - Comportamento Augment Agent Auto",
            "✅ Resposta completa entregue com sucesso!",
            "🔧 **FERRAMENTA EXECUTADA:",
            "Precisa de mais alguma coisa? Estou aqui para ajudar! 🚀"
        ]

        return any(indicator in response for indicator in completion_indicators)

    def _finalize_response(self, response: str) -> str:
        """Finalize the response with proper formatting and termination."""
        if not response:
            return "I'm ready to help. What would you like me to do?"

        # Ensure proper formatting for code snippets
        response = self._format_code_snippets(response)

        # Remove any trailing conversation markers
        response = re.sub(r'\n*(Human:|User:|Você:).*$', '', response, flags=re.DOTALL)

        return response.strip()

    def _format_code_snippets(self, text: str) -> str:
        """Format code snippets in Augment Agent Auto style."""
        # Convert regular code blocks to augment_code_snippet format
        def replace_code_block(match):
            language = match.group(1) or 'text'
            code = match.group(2)
            return f'<augment_code_snippet path="generated_code.{language}" mode="EXCERPT">\n````{language}\n{code}\n````\n</augment_code_snippet>'

        # Replace ```language\ncode\n``` with augment format
        text = re.sub(r'```(\w+)?\n(.*?)\n```', replace_code_block, text, flags=re.DOTALL)

        return text

    def _format_context_results(self, results: List[Dict[str, Any]]) -> str:
        """Format context results for prompt inclusion."""
        if not results:
            return ""

        formatted = "Relevant Context:\n"
        for i, result in enumerate(results, 1):
            path = result.get('path', 'unknown')
            score = result.get('score', 0.0)
            content = result.get('content', '')[:200] + "..."
            formatted += f"{i}. [{score:.3f}] {path}\n{content}\n\n"

        return formatted

    def _format_learning_examples(self, examples: List[Dict[str, Any]]) -> str:
        """Format learning examples for prompt inclusion."""
        if not examples:
            return ""

        formatted = "Relevant Examples:\n"
        for i, example in enumerate(examples[:3], 1):
            query = example.get('query', '')
            response = example.get('response', '')
            formatted += f"Example {i}:\nQuery: {query}\nResponse: {response[:100]}...\n\n"

        return formatted

    def _build_enhanced_prompt(self, message: str, context_text: str, memories: str,
                              examples_text: str, system_enhancement: str) -> str:
        """Build enhanced prompt matching Augment Agent Auto capabilities."""

        # Get available tools
        tools_list = []
        for tool_name, tool in self.tool_registry.tools.items():
            tools_list.append(f"{tool_name}: {tool.description}")

        prompt = f"""You are Enhanced Military Agent with Augment Agent Auto capabilities. Execute tools IMMEDIATELY when requested.

CURRENT REQUEST: {message}

CRITICAL RULES:
1. EXECUTE tools immediately - use {{tool.tool_name(param='value')}} syntax
2. For file analysis: {{tool.analyze_code(file_path='C:\\\\exact\\\\path\\\\file.py')}}
3. Be direct and concise
4. Always complete the requested task

{context_text}

AVAILABLE TOOLS: {', '.join([name for name in self.tool_registry.tools.keys()])}

Execute the requested tool NOW and provide results:"""

        return prompt

    def _process_tool_calls(self, text: str) -> str:
        """Process tool calls in response text with enhanced error handling."""
        # Multiple patterns for tool calls
        patterns = [
            r'\{tool\.(\w+)\(([^)]*)\)\}',  # {tool.name(args)}
            r'\{(\w+)\(([^)]*)\)\}',       # {name(args)}
            r'(\w+)\(([^)]*)\)',           # name(args) - direct calls
        ]

        processed_text = text

        # Look for tool calls in the response
        for pattern in patterns:
            matches = re.findall(pattern, text)

            for tool_name, args_str in matches:
                logger.info(f"Processing tool call: {tool_name}({args_str})")

                # Get tool
                tool = self.tool_registry.get(tool_name)

                if not tool:
                    logger.warning(f"Tool not found: {tool_name}")
                    continue

                # Parse arguments
                args = self._parse_tool_arguments(args_str)

                # Execute tool immediately
                try:
                    logger.info(f"Executing tool {tool_name} with args: {args}")
                    result = tool.execute(**args)
                    logger.info(f"Tool {tool_name} executed successfully")

                    # Create comprehensive result display
                    tool_result = f"\n🔧 FERRAMENTA EXECUTADA: {tool_name}\n"
                    tool_result += f"📋 PARÂMETROS: {args}\n"
                    tool_result += f"✅ RESULTADO:\n{result}\n"
                    tool_result += f"{'='*50}\n"

                    # Replace all possible variations of the tool call
                    variations = [
                        f"{{tool.{tool_name}({args_str})}}",
                        f"{{{tool_name}({args_str})}}",
                        f"{tool_name}({args_str})"
                    ]

                    for variation in variations:
                        if variation in processed_text:
                            processed_text = processed_text.replace(variation, tool_result)

                except Exception as e:
                    logger.error(f"Tool {tool_name} execution failed: {e}")

                    # Create error display
                    error_result = f"\n❌ ERRO NA FERRAMENTA: {tool_name}\n"
                    error_result += f"📋 PARÂMETROS: {args}\n"
                    error_result += f"🚫 ERRO: {str(e)}\n"
                    error_result += f"{'='*50}\n"

                    # Replace with error message
                    variations = [
                        f"{{tool.{tool_name}({args_str})}}",
                        f"{{{tool_name}({args_str})}}",
                        f"{tool_name}({args_str})"
                    ]

                    for variation in variations:
                        if variation in processed_text:
                            processed_text = processed_text.replace(variation, error_result)

        return processed_text

    def _parse_tool_arguments(self, args_str: str) -> Dict[str, Any]:
        """Parse tool arguments from string."""
        args = {}
        if not args_str.strip():
            return args

        # Split arguments
        parts = []
        current_part = ""
        in_quotes = False
        quote_char = None

        for char in args_str:
            if char in ['"', "'"] and not in_quotes:
                in_quotes = True
                quote_char = char
                current_part += char
            elif char == quote_char and in_quotes:
                in_quotes = False
                quote_char = None
                current_part += char
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip())
                current_part = ""
            else:
                current_part += char

        if current_part.strip():
            parts.append(current_part.strip())

        # Parse each part
        for part in parts:
            if '=' in part:
                key, value = part.split('=', 1)
                key = key.strip()
                value = value.strip()

                # Remove quotes
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]

                # Try to convert to appropriate type
                if value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                elif value.isdigit():
                    value = int(value)
                elif value.replace('.', '').isdigit():
                    value = float(value)

                args[key] = value

        return args

    def add_feedback(self, query: str, response: str, rating: int, comment: str = None) -> bool:
        """Add feedback to learning system."""
        return self.learning_system.add_feedback(query, response, rating, comment)

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive agent statistics."""
        return {
            'agent_metrics': self.metrics,
            'context_stats': self.context_engine.get_stats(),
            'memory_stats': self.memory_manager.get_stats() if hasattr(self.memory_manager, 'get_stats') else {},
            'learning_stats': self.learning_system.get_stats(),
            'conversation_length': len(self.conversation_history)
        }

    def verify_integrity(self) -> bool:
        """Verify system integrity."""
        try:
            # Check context engine
            context_ok = self.context_engine.verify_integrity()

            # Check military context engine
            military_ok = self.military_context.verify_integrity()

            # Check tool registry
            tools_ok = len(self.tool_registry.tools) > 0

            # Check conversation history
            history_ok = isinstance(self.conversation_history, list)

            integrity_result = context_ok and military_ok and tools_ok and history_ok

            logger.info(f"System integrity verification: {integrity_result}")
            return integrity_result

        except Exception as e:
            logger.error(f"Integrity verification failed: {e}")
            return False

    def clear_memories(self) -> bool:
        """Clear all memories."""
        return self.memory_manager.clear_memories()

    def add_memory(self, memory_text: str, tags: List[str] = None, importance: int = 3) -> bool:
        """Add memory to the system."""
        return self.memory_manager.add_memory(memory_text, tags, importance)

    def get_memories(self, limit: int = None, min_importance: int = 0) -> List[Dict[str, Any]]:
        """Get memories from the system."""
        return self.memory_manager.get_memories(limit, min_importance)

    def search_memories(self, query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search memories."""
        return self.memory_manager.search_memories(query, limit)
