"""
Enhanced Local LLM - Interface for GGUF models with Augment Agent Auto capabilities

This module implements a proper interface for local GGUF models,
specifically designed for the Enhanced Military Agent.

Features:
- Direct GGUF model loading
- No fallback mechanisms
- Military-grade error handling
- Performance optimization
"""

import os
import sys
import json
import logging
import time
from typing import Dict, List, Any, Optional, Union, Tuple

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - [Thread:%(thread)d] - %(message)s',
    handlers=[
        logging.FileHandler("enhanced_local_llm.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ENHANCED_LOCAL_LLM")

class LocalLLM:
    """
    Enhanced Local LLM interface for GGUF models.

    This class implements a proper interface for local GGUF models,
    designed specifically for the Enhanced Military Agent with 10/10 capabilities.

    Features:
    - Direct GGUF model loading using llama-cpp-python
    - No fallback mechanisms
    - Military-grade error handling
    - Performance optimization
    """

    def __init__(self, model_path: str, model_config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Enhanced Local LLM.

        Args:
            model_path (str): Path to the GGUF model file
            model_config (Dict[str, Any], optional): Model configuration

        Raises:
            ValueError: If model_path is not provided or invalid
            FileNotFoundError: If model file doesn't exist
            RuntimeError: If model loading fails
        """
        if not model_path:
            raise ValueError("Model path is required for Enhanced Military Agent")

        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")

        if not model_path.endswith('.gguf'):
            raise ValueError(f"Invalid model format. Expected .gguf file, got: {model_path}")

        self.model_path = model_path
        self.model_config = model_config or {}
        self.model = None

        # Performance metrics
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'total_tokens_generated': 0
        }

        logger.info(f"Initializing Enhanced Local LLM with model: {model_path}")
        self._initialize_gguf_model()

    def _initialize_gguf_model(self):
        """
        Initialize the GGUF model using llama-cpp-python.

        This method loads the GGUF model directly without any fallback mechanisms.
        """
        try:
            # Try to import llama-cpp-python
            try:
                from llama_cpp import Llama
                logger.info("Using llama-cpp-python for GGUF model loading")
            except ImportError:
                # Try alternative GGUF loaders
                try:
                    import ctransformers
                    logger.info("Using ctransformers for GGUF model loading")
                    self._initialize_with_ctransformers()
                    return
                except ImportError:
                    raise RuntimeError(
                        "No GGUF loader found. Please install llama-cpp-python or ctransformers:\n"
                        "pip install llama-cpp-python\n"
                        "or\n"
                        "pip install ctransformers"
                    )

            # Configure model parameters with fallback context sizes
            context_sizes = [8192, 4096, 2048, 1024]  # Try progressively smaller contexts

            for ctx_size in context_sizes:
                try:
                    model_params = {
                        'model_path': self.model_path,
                        'n_ctx': ctx_size,
                        'n_batch': min(512, ctx_size // 4),
                        'n_gpu_layers': self.model_config.get('n_gpu_layers', 0),
                        'use_mlock': False,  # Disable to save memory
                        'use_mmap': True,
                        'numa': False,
                        'verbose': False
                    }

                    # Load the model
                    logger.info(f"Loading GGUF model with parameters: {model_params}")
                    self.model = Llama(**model_params)
                    logger.info(f"[OK] Model loaded successfully with context size: {ctx_size}")
                    break  # Success, exit loop
                except Exception as e:
                    logger.warning(f"Failed to load with context size {ctx_size}: {e}")
                    if ctx_size == context_sizes[-1]:  # Last attempt
                        raise e
                    continue

            logger.info("[OK] Enhanced Local LLM initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize GGUF model: {e}")
            raise RuntimeError(f"Enhanced Military Agent requires a working LLM. Model loading failed: {e}")

    def _initialize_with_ctransformers(self):
        """Initialize using ctransformers as alternative GGUF loader."""
        try:
            from ctransformers import AutoModelForCausalLM

            model_params = {
                'model_path': self.model_path,
                'model_type': 'llama',  # Most GGUF models are llama-based
                'context_length': 4096,  # Conservative context window
                'batch_size': 512,  # Conservative batch size
                'gpu_layers': self.model_config.get('n_gpu_layers', 0)
            }

            logger.info(f"Loading GGUF model with ctransformers: {model_params}")
            self.model = AutoModelForCausalLM.from_pretrained(**model_params)

            logger.info("[OK] Enhanced Local LLM initialized with ctransformers")

        except Exception as e:
            logger.error(f"Failed to initialize with ctransformers: {e}")
            raise RuntimeError(f"Enhanced Military Agent requires a working LLM. ctransformers loading failed: {e}")

    def generate(self, prompt: str, max_tokens: int = 1000, temperature: float = 0.7,
                top_p: float = 0.9, top_k: int = 50, stop_sequences: Optional[List[str]] = None) -> str:
        """
        Generate text from a prompt using the GGUF model.

        Args:
            prompt (str): Input prompt for text generation
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Sampling temperature (0.0 to 1.0)
            top_p (float): Nucleus sampling probability
            top_k (int): Top-k sampling parameter
            stop_sequences (List[str], optional): Sequences that stop generation

        Returns:
            str: Generated text

        Raises:
            RuntimeError: If model is not initialized or generation fails
        """
        if not self.model:
            raise RuntimeError("Enhanced Military Agent LLM not initialized")

        start_time = time.time()
        self.stats['total_requests'] += 1

        try:
            # Prepare generation parameters
            generation_params = {
                'max_tokens': max_tokens,
                'temperature': temperature,
                'top_p': top_p,
                'top_k': top_k,
                'echo': False,  # Don't echo the prompt
                'stop': stop_sequences or []
            }

            # Generate text using llama-cpp-python
            if hasattr(self.model, '__call__'):
                # llama-cpp-python interface
                response = self.model(prompt, **generation_params)

                if isinstance(response, dict) and 'choices' in response:
                    generated_text = response['choices'][0]['text']
                elif isinstance(response, list) and len(response) > 0:
                    generated_text = response[0].get('generated_text', '')
                else:
                    generated_text = str(response)

            elif hasattr(self.model, 'generate'):
                # ctransformers interface
                generated_text = self.model.generate(prompt, **generation_params)
            else:
                raise RuntimeError("Unknown model interface")

            # Clean up the generated text
            generated_text = generated_text.strip()

            # Update statistics
            generation_time = time.time() - start_time
            self.stats['successful_requests'] += 1
            self.stats['total_tokens_generated'] += len(generated_text.split())
            self.stats['average_response_time'] = (
                (self.stats['average_response_time'] * (self.stats['successful_requests'] - 1) + generation_time)
                / self.stats['successful_requests']
            )

            logger.info(f"[OK] Text generated in {generation_time:.2f}s ({len(generated_text)} chars)")

            return generated_text

        except Exception as e:
            self.stats['failed_requests'] += 1
            error_msg = f"Enhanced Military Agent LLM generation failed: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return self.stats.copy()

    def is_ready(self) -> bool:
        """Check if the model is ready for inference."""
        return self.model is not None


